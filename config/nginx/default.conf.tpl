upstream upstream_softgate_api {
  server ${SOFTGATE_MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_default_api {
  server ${MANAGEMENT_API} fail_timeout=30s max_fails=100;
}

upstream upstream_integration_api {
  server ${INTEGRATION_API} fail_timeout=30s max_fails=100;
}

upstream upstream_lobby_build_api {
  server ${LOBBY_BUILD_API} fail_timeout=30s max_fails=100;
}

server {
  #listen 443 reuseport ssl http2;
  listen 80 reuseport default_server;
  server_name  localhost;

  userid_name 'uid';
  userid_path '/; HttpOnly';
  userid_expires 365d;
  userid         on;
  server_tokens off;

  gzip on;
  gzip_comp_level 3;
  gzip_static on;
  gzip_min_length 2048;
  gzip_buffers      16 8k;
  gzip_vary         on;
  gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml
  image/svg+xml application/xml+rss image/x-icon image/bmp;
  gzip_disable "msie6";

  root /usr/share/nginx/html/;

  proxy_buffer_size 128k;
  proxy_buffers 4 256k;
  proxy_busy_buffers_size 256k;

  large_client_header_buffers 4 32k;
  client_max_body_size 32m;

  proxy_connect_timeout 300;
  proxy_send_timeout 300;
  proxy_read_timeout 300;
  send_timeout 300;
  expires -1;
  etag off;

  location /favicon.ico {
    if ($host = $SOFTGATE_HOST) {
      rewrite ^ /img/softgate/favicon.ico break;
    }
    rewrite ^ /favicon.ico break;
  }

  location / {
    index /index.html;
    try_files $uri $uri/ /index.html =404;
    access_log off;
  }

  location /api/config {
    default_type application/json;
    set $ENV_CONFIG '';
    set $ENV_CONFIG '${ENV_CONFIG} {';
    set $ENV_CONFIG '${ENV_CONFIG} "host": "${host}",';
    set $ENV_CONFIG '${ENV_CONFIG} "gosGameProviderCode": "${GOS_GAME_PROVIDER_CODE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "liveChatLicence": "${LIVE_CHAT_LICENCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithBalance": "${CSV_MAX_PLAYERS_WITH_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "csvMaxPlayersWithoutBalance": "${CSV_MAX_PLAYERS_WITHOUT_BALANCE}",';
    set $ENV_CONFIG '${ENV_CONFIG} "promotions": {';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvFileSize": "${PROMO_PARTICIPANTS_MAX_CSV_FILE_SIZE}",';
    set $ENV_CONFIG '${ENV_CONFIG}   "maxCsvLines": "${PROMO_PARTICIPANTS_MAX_CSV_ITEMS}"';
    set $ENV_CONFIG '${ENV_CONFIG} },';
    if ($host = $SOFTGATE_HOST) {
      set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${SOFTGATE_BRIDGE_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${SOFTGATE_LOGIN_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
      set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${SOFTGATE_CASINO_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${SOFTGATE_ENGAGEMENT_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${SOFTGATE_DATA_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${SOFTGATE_STUDIO_HUB_URL}"';
      set $ENV_CONFIG '${ENV_CONFIG} },';
      set $ENV_CONFIG '${ENV_CONFIG} "logo": {';
      set $ENV_CONFIG '${ENV_CONFIG}   "main": "/img/softgate/logo.png",';
      set $ENV_CONFIG '${ENV_CONFIG}   "solo": "/img/softgate/logo.png",';
      set $ENV_CONFIG '${ENV_CONFIG}   "white": ""';
      set $ENV_CONFIG '${ENV_CONFIG} },';
    }
    if ($host != $SOFTGATE_HOST) {
      set $ENV_CONFIG '${ENV_CONFIG} "bridge": "${BRIDGE_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "loginUrl": "${LOGIN_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG} "hubs": {';
      set $ENV_CONFIG '${ENV_CONFIG}   "casino": "${CASINO_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "engagement": "${ENGAGEMENT_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "analytics": "${DATA_HUB_URL}",';
      set $ENV_CONFIG '${ENV_CONFIG}   "studio": "${STUDIO_HUB_URL}"';
      set $ENV_CONFIG '${ENV_CONFIG} },';
    }
    set $ENV_CONFIG '${ENV_CONFIG} "envName": "${ENV_NAME}",';
    set $ENV_CONFIG '${ENV_CONFIG} "locationName": "${LOCATION_NAME}"';
    set $ENV_CONFIG '${ENV_CONFIG} }';
    return 200 $ENV_CONFIG;
  }

  location /v1 {
    proxy_hide_header 'Access-Control-Allow-Origin';
    proxy_hide_header 'Access-Control-Allow-Methods';
    proxy_hide_header 'Access-Control-Allow-Headers';
    proxy_hide_header 'Access-Control-Allow-Credentials';
    proxy_hide_header 'Access-Control-Max-Age';

    if ($request_method = 'OPTIONS') {
      add_header 'Access-Control-Allow-Origin' $http_origin always;
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, DELETE, PATCH, PUT' always;
      add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;
      add_header 'Access-Control-Allow-Credentials' 'true' always;
      add_header 'Access-Control-Max-Age' 2592000 always;
      return 204;
    }

    add_header 'Access-Control-Allow-Origin' $http_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;
    add_header 'Access-Control-Allow-Headers' 'x-access-token, accept, content-type' always;

    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/v1;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/v1;
    }
  }

  location /v2 {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/v2;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/v2;
    }
  }

  location /api/v1/srt {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/auth-gateway/srt-api/srt;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/auth-gateway/srt-api/srt;
    }
  }

  location /auth-gateway {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/auth-gateway;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/auth-gateway;
    }
  }

  # Game provider
  location /gameprovider/v1 {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/gameprovider/v1;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/gameprovider/v1;
    }
  }

  location /gameprovider/v2 {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/gameprovider/v2;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/gameprovider/v2;
    }
  }

  # Players
  location /player/v1 {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/player/v1;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/player/v1;
    }
  }

  # site
  location /site/v1 {
    if ($host = $SOFTGATE_HOST) {
      proxy_pass http://upstream_softgate_api/site/v1;
    }
    if ($host != $SOFTGATE_HOST) {
      proxy_pass http://upstream_default_api/site/v1;
    }
  }

  location /api/v1/integration {
    proxy_pass http://upstream_integration_api/v1;
  }

  location /api/v1/lobby-build/ {
    proxy_pass http://upstream_lobby_build_api/;
  }

  location /cdn/ {
    proxy_pass ${GAMES_URL}/;
  }

  location /widgets {
    proxy_pass ${LOBBY_WIDGETS_URL}/widgets;
  }
}

